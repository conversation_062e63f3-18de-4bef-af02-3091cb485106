package com.fe.platform.controller;

import com.fe.platform.dto.common.Result;
import com.fe.platform.dto.risk.RiskDashboardDTO;
import com.fe.platform.service.RiskService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 风险控制器
 */
@RestController
@RequestMapping("/risk")
@RequiredArgsConstructor
public class RiskController {

    private final RiskService riskService;

    /**
     * 获取风险仪表盘数据
     * @return 风险仪表盘数据
     */
    @GetMapping("/dashboard")
    public Result<RiskDashboardDTO> getRiskDashboard() {
        return Result.success(riskService.getRiskDashboard());
    }

    /**
     * 获取汇率变化分析数据
     * @param period 期间（3m-3个月, 6m-6个月, 12m-12个月）
     * @return 汇率变化分析数据
     */
    @GetMapping("/exchange-rate-analysis")
    public Result<RiskDashboardDTO.ExchangeRateAnalysisDTO> getExchangeRateAnalysis(
            @RequestParam(value = "period", defaultValue = "3m") String period) {
        return Result.success(riskService.getExchangeRateAnalysis(period));
    }

    /**
     * 获取风险模型数据
     * @return 风险模型数据列表
     */
    @GetMapping("/risk-models")
    public Result<List<RiskDashboardDTO.RiskModelDTO>> getRiskModels() {
        return Result.success(riskService.getRiskModels());
    }

    /**
     * 获取敞口分析数据
     * @return 敞口分析数据
     */
    @GetMapping("/exposure-analysis")
    public Result<RiskDashboardDTO.ExposureAnalysisDTO> getExposureAnalysis() {
        return Result.success(riskService.getExposureAnalysis());
    }

    /**
     * 获取结汇建议数据
     * @return 结汇建议数据列表
     */
    @GetMapping("/recommendations")
    public Result<List<RiskDashboardDTO.RecommendationDTO>> getRecommendations() {
        return Result.success(riskService.getRecommendations());
    }

    /**
     * 重新计算风险模型
     * @return 操作结果
     */
    @PostMapping("/recalculate-models")
    public Result<String> recalculateRiskModels() {
        riskService.recalculateRiskModels();
        return Result.success("风险模型重新计算完成");
    }
}
