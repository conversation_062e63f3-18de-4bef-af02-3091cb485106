package com.fe.platform.service;

import com.fe.platform.dto.risk.RiskDashboardDTO;

/**
 * 风险服务接口
 */
public interface RiskService {
    
    /**
     * 获取风险仪表盘数据
     * @return 风险仪表盘数据
     */
    RiskDashboardDTO getRiskDashboard();
    
    /**
     * 获取汇率变化分析数据
     * @param period 期间（3m-3个月, 6m-6个月, 12m-12个月）
     * @return 汇率变化分析数据
     */
    RiskDashboardDTO.ExchangeRateAnalysisDTO getExchangeRateAnalysis(String period);
    
    /**
     * 获取风险模型数据
     * @return 风险模型数据列表
     */
    java.util.List<RiskDashboardDTO.RiskModelDTO> getRiskModels();
    
    /**
     * 获取敞口分析数据
     * @return 敞口分析数据
     */
    RiskDashboardDTO.ExposureAnalysisDTO getExposureAnalysis();
    
    /**
     * 获取结汇建议数据
     * @return 结汇建议数据列表
     */
    java.util.List<RiskDashboardDTO.RecommendationDTO> getRecommendations();

    /**
     * 重新计算风险模型
     */
    void recalculateRiskModels();
}
