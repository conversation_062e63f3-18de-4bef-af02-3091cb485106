/* 移动端专用样式 */

/* 防止iOS Safari的橡皮筋效果 */
html, body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* 移动端触摸优化 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许文本选择的元素 */
input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* 移动端安全区域适配 */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(env(safe-area-inset-top), 0px);
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 0px);
  }
  
  .safe-area-inset-left {
    padding-left: max(env(safe-area-inset-left), 0px);
  }
  
  .safe-area-inset-right {
    padding-right: max(env(safe-area-inset-right), 0px);
  }
}

/* 移动端滚动条隐藏 */
.mobile-scroll {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.mobile-scroll::-webkit-scrollbar {
  display: none;
}

/* 移动端按钮优化 */
.mobile-button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  outline: none;
}

.mobile-button:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 移动端输入框优化 */
.mobile-input {
  min-height: 44px;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  font-size: 16px;
  background-color: #fff;
  transition: border-color 0.2s ease;
}

.mobile-input:focus {
  border-color: #165DFF;
  outline: none;
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

/* 移动端卡片优化 */
.mobile-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 16px;
  margin-bottom: 12px;
  transition: box-shadow 0.2s ease;
}

.mobile-card:active {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端列表优化 */
.mobile-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-list-item {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  transition: background-color 0.2s ease;
}

.mobile-list-item:active {
  background-color: #f5f7fa;
}

.mobile-list-item:last-child {
  border-bottom: none;
}

/* 移动端导航优化 */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 8px 0;
  padding-bottom: max(8px, env(safe-area-inset-bottom));
  z-index: 1000;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  color: #666;
  text-decoration: none;
  transition: color 0.2s ease;
  min-height: 44px;
  justify-content: center;
}

.mobile-nav-item.active {
  color: #165DFF;
}

.mobile-nav-item i {
  font-size: 20px;
  margin-bottom: 4px;
}

.mobile-nav-item span {
  font-size: 12px;
}

/* 移动端模态框优化 */
.mobile-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.mobile-modal-content {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  padding: 20px;
  padding-bottom: max(20px, env(safe-area-inset-bottom));
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.mobile-modal.show .mobile-modal-content {
  transform: translateY(0);
}

/* 移动端表格优化 */
.mobile-table {
  width: 100%;
  border-collapse: collapse;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.mobile-table th,
.mobile-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.mobile-table th {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #333;
}

.mobile-table tr:last-child td {
  border-bottom: none;
}

/* 移动端图表容器优化 */
.mobile-chart-container {
  position: relative;
  width: 100%;
  height: 200px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 移动端加载状态 */
.mobile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.mobile-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f0f0f0;
  border-top: 3px solid #165DFF;
  border-radius: 50%;
  animation: mobile-spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes mobile-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端空状态 */
.mobile-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
  text-align: center;
}

.mobile-empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.mobile-empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.mobile-empty-description {
  font-size: 14px;
  opacity: 0.7;
}

/* 移动端工具栏 */
.mobile-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  gap: 12px;
}

.mobile-toolbar-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.mobile-toolbar-action {
  min-height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
}

.mobile-toolbar-action:active {
  background-color: #f5f7fa;
  color: #165DFF;
}

/* 移动端特定的Element Plus组件样式覆盖 */
@media (max-width: 768px) {
  .el-button {
    min-height: 40px;
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .el-input__inner {
    height: 40px;
    font-size: 16px;
  }
  
  .el-select .el-input__inner {
    height: 40px;
  }
  
  .el-form-item {
    margin-bottom: 16px;
  }
  
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
  
  .el-message-box {
    width: 90% !important;
  }
  
  .el-table th,
  .el-table td {
    padding: 8px 4px;
    font-size: 13px;
  }
  
  .el-pagination {
    text-align: center;
  }
  
  .el-pagination .el-pager li {
    min-width: 32px;
    height: 32px;
    line-height: 32px;
  }
}
