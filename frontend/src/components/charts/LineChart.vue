<script setup>
import { ref, onMounted, watch } from 'vue'
import { Line } from 'vue-chartjs'
import { Chart as ChartJS, Title, Tooltip, Legend, LineElement, CategoryScale, LinearScale, PointElement } from 'chart.js'

// 注册Chart.js组件
ChartJS.register(Title, Tooltip, Legend, LineElement, CategoryScale, LinearScale, PointElement)

const props = defineProps({
  // 图表数据
  chartData: {
    type: Object,
    required: true
  },
  // 图表选项
  chartOptions: {
    type: Object,
    default: () => ({})
  },
  // 图表高度
  height: {
    type: Number,
    default: 300
  }
})

// Chart实例
const chartInstance = ref(null)

// 检测是否为移动端
const isMobile = () => window.innerWidth <= 768

// 默认选项
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      position: 'top',
      labels: {
        boxWidth: isMobile() ? 8 : 12,
        usePointStyle: true,
        pointStyle: 'circle',
        font: {
          size: isMobile() ? 11 : 12
        },
        padding: isMobile() ? 8 : 10
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleFont: {
        size: isMobile() ? 12 : 14
      },
      bodyFont: {
        size: isMobile() ? 11 : 12
      },
      padding: isMobile() ? 8 : 12,
      cornerRadius: 6
    }
  },
  elements: {
    line: {
      tension: 0.4,
      borderWidth: isMobile() ? 1.5 : 2
    },
    point: {
      radius: isMobile() ? 1 : 2,
      hoverRadius: isMobile() ? 3 : 4,
      borderWidth: isMobile() ? 1 : 2
    }
  },
  scales: {
    y: {
      beginAtZero: false,
      grid: {
        drawBorder: false,
        color: 'rgba(0, 0, 0, 0.05)'
      },
      ticks: {
        font: {
          size: isMobile() ? 10 : 11
        },
        maxTicksLimit: isMobile() ? 5 : 8,
        padding: isMobile() ? 4 : 8
      }
    },
    x: {
      grid: {
        display: false
      },
      ticks: {
        font: {
          size: isMobile() ? 10 : 11
        },
        maxTicksLimit: isMobile() ? 4 : 8,
        padding: isMobile() ? 4 : 8,
        maxRotation: isMobile() ? 45 : 0
      }
    }
  }
}

// 合并选项
const options = ref({
  ...defaultOptions,
  ...props.chartOptions
})

// 监听属性变化
watch(() => props.chartOptions, (newVal) => {
  options.value = {
    ...defaultOptions,
    ...newVal
  }
}, { deep: true })

// 监听数据变化
watch(() => props.chartData, (newVal) => {
  if (chartInstance.value) {
    chartInstance.value.update()
  }
}, { deep: true })

// 组件挂载后
onMounted(() => {
  // 创建图表
})
</script>

<template>
  <div class="line-chart-container" :style="{ height: `${height}px` }">
    <Line
      ref="chartInstance"
      :data="chartData"
      :options="options"
      :height="height"
    />
  </div>
</template>

<style scoped>
.line-chart-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .line-chart-container {
    min-height: 200px;
  }
}

@media (max-width: 480px) {
  .line-chart-container {
    min-height: 180px;
  }
}
</style>
