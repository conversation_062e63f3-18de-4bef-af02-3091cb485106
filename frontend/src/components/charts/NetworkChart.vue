<template>
  <div class="network-chart-container" ref="chartContainer">
    <svg :width="width" :height="height" class="network-svg">
      <!-- 连接线 -->
      <g class="links">
        <path
          v-for="(link, index) in links"
          :key="`link-${index}`"
          :d="link.path"
          :stroke="link.color"
          :stroke-width="link.width"
          fill="none"
          :marker-end="'url(#arrowhead-' + index + ')'"
        />
      </g>
      
      <!-- 节点 -->
      <g class="nodes">
        <g
          v-for="(node, index) in nodes"
          :key="`node-${index}`"
          :transform="`translate(${node.x}, ${node.y})`"
          class="node"
        >
          <circle
            :r="node.radius"
            :fill="node.color"
            :stroke="node.borderColor"
            :stroke-width="2"
          />
          <text
            :dy="5"
            text-anchor="middle"
            class="node-label"
            :fill="node.textColor"
          >
            {{ node.label }}
          </text>
        </g>
      </g>
      
      <!-- 箭头标记 -->
      <defs>
        <marker
          v-for="(link, index) in links"
          :key="`marker-${index}`"
          :id="`arrowhead-${index}`"
          markerWidth="10"
          markerHeight="7"
          refX="9"
          refY="3.5"
          orient="auto"
        >
          <polygon
            points="0 0, 10 3.5, 0 7"
            :fill="link.color"
          />
        </marker>
      </defs>
    </svg>
    
    <!-- 图例 -->
    <div class="legend">
      <div class="legend-item">
        <div class="legend-color" style="background-color: #165DFF;"></div>
        <span>升值状态</span>
      </div>
      <div class="legend-item">
        <div class="legend-color" style="background-color: #F53F3F;"></div>
        <span>贬值状态</span>
      </div>
      <div class="legend-item">
        <div class="legend-color" style="background-color: #86909C;"></div>
        <span>稳定状态</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      nodes: [],
      transitions: []
    })
  },
  width: {
    type: Number,
    default: 400
  },
  height: {
    type: Number,
    default: 300
  }
})

const chartContainer = ref(null)

// 节点配置
const nodes = computed(() => {
  const nodeData = props.chartData.nodes || [
    { id: 'appreciation', label: '升值', probability: 0.38 },
    { id: 'depreciation', label: '贬值', probability: 0.62 },
    { id: 'stable', label: '稳定', probability: 0.15 }
  ]
  
  const centerX = props.width / 2
  const centerY = props.height / 2
  const radius = Math.min(props.width, props.height) * 0.3
  
  return nodeData.map((node, index) => {
    const angle = (index * 2 * Math.PI) / nodeData.length - Math.PI / 2
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    
    return {
      ...node,
      x,
      y,
      radius: 30 + node.probability * 20,
      color: node.id === 'appreciation' ? '#165DFF' : 
             node.id === 'depreciation' ? '#F53F3F' : '#86909C',
      borderColor: '#fff',
      textColor: '#fff'
    }
  })
})

// 连接线配置
const links = computed(() => {
  const transitions = props.chartData.transitions || [
    { from: 'appreciation', to: 'depreciation', probability: 0.25 },
    { from: 'depreciation', to: 'appreciation', probability: 0.18 },
    { from: 'appreciation', to: 'stable', probability: 0.15 },
    { from: 'stable', to: 'appreciation', probability: 0.12 },
    { from: 'depreciation', to: 'stable', probability: 0.20 },
    { from: 'stable', to: 'depreciation', probability: 0.28 }
  ]
  
  return transitions.map(transition => {
    const fromNode = nodes.value.find(n => n.id === transition.from)
    const toNode = nodes.value.find(n => n.id === transition.to)
    
    if (!fromNode || !toNode) return null
    
    // 计算曲线路径
    const dx = toNode.x - fromNode.x
    const dy = toNode.y - fromNode.y
    const dr = Math.sqrt(dx * dx + dy * dy) * 0.3
    
    const path = `M${fromNode.x},${fromNode.y}A${dr},${dr} 0 0,1 ${toNode.x},${toNode.y}`
    
    return {
      path,
      color: `rgba(22, 93, 255, ${0.3 + transition.probability * 0.7})`,
      width: 2 + transition.probability * 4,
      probability: transition.probability
    }
  }).filter(Boolean)
})

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style scoped>
.network-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
}

.network-svg {
  width: 100%;
  height: 100%;
}

.node {
  cursor: pointer;
  transition: all 0.3s ease;
}

.node:hover {
  transform: scale(1.1);
}

.node-label {
  font-size: 12px;
  font-weight: bold;
  pointer-events: none;
}

.legend {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}
</style>
