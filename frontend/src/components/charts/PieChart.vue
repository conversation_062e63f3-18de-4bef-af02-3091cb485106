<script setup>
import { ref, onMounted, watch } from 'vue'
import { Pie } from 'vue-chartjs'
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js'

// 注册Chart.js组件
ChartJS.register(ArcElement, Tooltip, Legend)

const props = defineProps({
  // 图表数据
  chartData: {
    type: Object,
    required: true
  },
  // 图表选项
  chartOptions: {
    type: Object,
    default: () => ({})
  },
  // 图表高度
  height: {
    type: Number,
    default: 300
  }
})

// Chart实例
const chartInstance = ref(null)

// 检测是否为移动端
const isMobile = () => window.innerWidth <= 768

// 默认选项
const defaultOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: isMobile() ? 'bottom' : 'right',
      labels: {
        boxWidth: isMobile() ? 8 : 12,
        usePointStyle: true,
        pointStyle: 'circle',
        font: {
          size: isMobile() ? 11 : 12
        },
        padding: isMobile() ? 8 : 10
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleFont: {
        size: isMobile() ? 12 : 14
      },
      bodyFont: {
        size: isMobile() ? 11 : 12
      },
      padding: isMobile() ? 8 : 12,
      cornerRadius: 6
    }
  },
  elements: {
    arc: {
      borderWidth: isMobile() ? 1 : 2,
      hoverBorderWidth: isMobile() ? 2 : 3
    }
  }
}

// 合并选项
const options = ref({
  ...defaultOptions,
  ...props.chartOptions
})

// 监听属性变化
watch(() => props.chartOptions, (newVal) => {
  options.value = {
    ...defaultOptions,
    ...newVal
  }
}, { deep: true })

// 监听数据变化
watch(() => props.chartData, (newVal) => {
  if (chartInstance.value) {
    chartInstance.value.update()
  }
}, { deep: true })

// 组件挂载后
onMounted(() => {
  // 创建图表
})
</script>

<template>
  <div class="pie-chart-container" :style="{ height: `${height}px` }">
    <Pie
      ref="chartInstance"
      :data="chartData"
      :options="options"
      :height="height"
    />
  </div>
</template>

<style scoped>
.pie-chart-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .pie-chart-container {
    min-height: 250px;
  }
}

@media (max-width: 480px) {
  .pie-chart-container {
    min-height: 220px;
  }
}
</style>
