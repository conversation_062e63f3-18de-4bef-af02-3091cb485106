<script setup>
defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 是否显示更多按钮
  showMore: {
    type: Boolean,
    default: false
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 卡片高度
  height: {
    type: [String, Number],
    default: 'auto'
  }
})

// 更多按钮点击事件
const emit = defineEmits(['more'])
const handleMore = () => {
  emit('more')
}
</script>

<template>
  <div class="data-card" :style="{ height: typeof height === 'number' ? `${height}px` : height }">
    <div class="card-header">
      <div class="card-title">{{ title }}</div>
      <div v-if="showMore" class="more-btn" @click="handleMore">
        <span>更多</span>
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>

    <div class="card-content">
      <el-skeleton v-if="loading" :rows="3" animated />
      <slot v-else></slot>
    </div>
  </div>
</template>

<style scoped>
.data-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: box-shadow 0.3s ease;
}

.data-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  flex: 1;
  min-width: 0;
}

.more-btn {
  color: #165DFF;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
  white-space: nowrap;
}

.more-btn:hover {
  background-color: rgba(22, 93, 255, 0.05);
}

.more-btn i {
  margin-left: 4px;
}

.card-content {
  flex: 1;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-card {
    padding: var(--mobile-padding, 12px);
    border-radius: 6px;
    margin-bottom: var(--mobile-margin, 8px);
  }

  .card-header {
    margin-bottom: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .card-title {
    font-size: 15px;
  }

  .more-btn {
    font-size: 13px;
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .data-card {
    padding: 10px;
  }

  .card-header {
    margin-bottom: 10px;
  }

  .card-title {
    font-size: 14px;
  }

  .more-btn {
    font-size: 12px;
    padding: 2px 6px;
  }
}
</style>
