<script setup>
import AppHeader from './AppHeader.vue'
</script>

<template>
  <div class="app-layout">
    <AppHeader />
    <div class="main-container">
      <div class="content-container">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.main-container {
  margin-top: 60px;
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  background-color: #f5f7fa;
}

.content-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-container {
    padding: var(--tablet-padding, 16px);
  }

  .content-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .main-container {
    margin-top: var(--mobile-header-height, 56px);
    padding: var(--mobile-padding, 12px);
  }

  .content-container {
    padding: 0;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 8px;
  }
}
</style>
