import request from './request'

/**
 * 仪表盘 API
 */
export const dashboardAPI = {
  /**
   * 获取仪表盘概览数据
   * @param {Object} params - 请求参数
   * @param {string} params.currencyPair - 货币对
   * @param {string} params.timePeriod - 时间周期
   * @returns {Promise} 响应数据
   */
  getDashboardSummary(params) {
    return request.get('/dashboard/summary', params)
  }
}

/**
 * 财务 API
 */
export const financeAPI = {
  /**
   * 获取财务仪表盘数据
   * @returns {Promise} 响应数据
   */
  getFinanceDashboard() {
    return request.get('/finance/dashboard')
  },
  
  /**
   * 获取汇兑损益数据
   * @param {Object} params - 请求参数
   * @param {string} params.period - 时间周期
   * @returns {Promise} 响应数据
   */
  getExchangeGain(params) {
    return request.get('/finance/exchange-gain', params)
  },
  
  /**
   * 获取汇率趋势数据
   * @param {Object} params - 请求参数
   * @param {string} params.currencyPair - 货币对
   * @returns {Promise} 响应数据
   */
  getExchangeRate(params) {
    return request.get('/finance/exchange-rate', params)
  },

  /**
   * 获取已实现损益详情
   * @returns {Promise} 响应数据
   */
  getRealizedGainDetails() {
    return request.get('/finance/realized-gain-details')
  },

  /**
   * 获取现金流量详情
   * @returns {Promise} 响应数据
   */
  getCashFlowDetail() {
    return request.get('/finance/cash-flow-detail')
  },

  /**
   * 获取数据输入面板配置
   * @returns {Promise} 响应数据
   */
  getDataInputConfig() {
    return request.get('/finance/data-input-config')
  },

  /**
   * 重新计算财务数据
   * @param {Object} inputData - 输入数据
   * @returns {Promise} 响应数据
   */
  recalculateFinanceData(inputData) {
    return request.post('/finance/recalculate', inputData)
  }
}

/**
 * 风险 API
 */
export const riskAPI = {
  /**
   * 获取风险仪表盘数据
   * @returns {Promise} 响应数据
   */
  getRiskDashboard() {
    return request.get('/risk/dashboard')
  },

  /**
   * 获取汇率变化分析数据
   * @param {Object} params - 请求参数
   * @param {string} params.period - 时间周期
   * @returns {Promise} 响应数据
   */
  getExchangeRateAnalysis(params) {
    return request.get('/risk/exchange-rate-analysis', params)
  },

  /**
   * 获取风险模型数据
   * @returns {Promise} 响应数据
   */
  getRiskModels() {
    return request.get('/risk/risk-models')
  },

  /**
   * 获取敞口分析数据
   * @returns {Promise} 响应数据
   */
  getExposureAnalysis() {
    return request.get('/risk/exposure-analysis')
  },

  /**
   * 获取结汇建议数据
   * @returns {Promise} 响应数据
   */
  getRecommendations() {
    return request.get('/risk/recommendations')
  },

  /**
   * 重新计算风险模型
   * @returns {Promise} 响应数据
   */
  recalculateRiskModels() {
    return request.post('/risk/recalculate-models')
  }
}
