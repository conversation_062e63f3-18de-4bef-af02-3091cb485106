import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
import { formatDate } from './format'

/**
 * 将HTML元素导出为PDF
 * @param {HTMLElement} element - 要导出的HTML元素
 * @param {string} filename - PDF文件名（不包含扩展名）
 * @param {Object} options - 导出配置选项
 * @returns {Promise<void>}
 */
export async function exportToPDF(element, filename, options = {}) {
  try {
    // 默认配置
    const defaultOptions = {
      scale: 2, // 提高清晰度
      useCORS: true, // 允许加载跨域图片
      logging: false, // 关闭日志
      backgroundColor: '#ffffff', // 设置背景色
      scrollY: 0, // 垂直滚动位置
      scrollX: 0, // 水平滚动位置
      windowWidth: document.documentElement.offsetWidth, // 窗口宽度
      windowHeight: document.documentElement.offsetHeight // 窗口高度
    }
    
    // 合并配置
    const settings = { ...defaultOptions, ...options }
    
    // 保存原始滚动位置
    const originalScrollY = window.scrollY
    const originalScrollX = window.scrollX
    
    // 临时修改元素样式以确保完整捕获
    const originalStyle = {
      overflow: element.style.overflow,
      height: element.style.height,
      position: element.style.position
    }
    
    // 设置元素样式确保所有内容可见
    element.style.overflow = 'visible'
    element.style.height = 'auto'
    
    // 创建canvas
    const canvas = await html2canvas(element, {
      scale: settings.scale,
      useCORS: settings.useCORS,
      logging: settings.logging,
      backgroundColor: settings.backgroundColor,
      windowWidth: settings.windowWidth,
      windowHeight: settings.windowHeight,
      scrollX: settings.scrollX,
      scrollY: settings.scrollY,
      allowTaint: true, // 允许污染canvas
      height: element.scrollHeight, // 使用元素的完整高度
      width: element.scrollWidth // 使用元素的完整宽度
    })
    
    // 恢复元素原始样式
    element.style.overflow = originalStyle.overflow
    element.style.height = originalStyle.height
    element.style.position = originalStyle.position
    
    // 恢复原始滚动位置
    window.scrollTo(originalScrollX, originalScrollY)

    // 获取canvas的宽度和高度
    const imgWidth = 595.28 // A4纸的宽度（单位：pt）
    const pageHeight = 841.89 // A4纸的高度（单位：pt）
    const imgHeight = (canvas.height * imgWidth) / canvas.width
    
    // 创建PDF实例
    const pdf = new jsPDF('p', 'pt', 'a4')
    
    // 根据内容高度计算页数
    let heightLeft = imgHeight
    let position = 0
    
    // 添加第一页
    pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, position, imgWidth, imgHeight)
    heightLeft -= pageHeight
    
    // 如果内容超过一页，添加更多页面
    while (heightLeft > 0) {
      position -= pageHeight // 调整位置到下一页
      pdf.addPage() // 添加新页
      pdf.addImage(
        canvas.toDataURL('image/jpeg', 1.0),
        'JPEG',
        0,
        position,
        imgWidth,
        imgHeight
      )
      heightLeft -= pageHeight
    }

    // 生成PDF文件名
    const timestamp = formatDate(new Date(), 'YYYYMMDDHHmm')
    const fullFilename = `${filename}_${timestamp}.pdf`

    // 保存PDF
    pdf.save(fullFilename)
  } catch (error) {
    console.error('导出PDF失败:', error)
    throw error
  }
} 