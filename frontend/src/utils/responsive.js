/**
 * 响应式工具函数
 * 用于检测设备类型和屏幕尺寸
 */

// 断点定义
export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1200,
  desktop: 1200
}

/**
 * 检测是否为移动端设备
 * @returns {boolean}
 */
export const isMobile = () => {
  return window.innerWidth <= BREAKPOINTS.mobile
}

/**
 * 检测是否为平板设备
 * @returns {boolean}
 */
export const isTablet = () => {
  return window.innerWidth > BREAKPOINTS.mobile && window.innerWidth <= BREAKPOINTS.tablet
}

/**
 * 检测是否为桌面设备
 * @returns {boolean}
 */
export const isDesktop = () => {
  return window.innerWidth > BREAKPOINTS.desktop
}

/**
 * 获取当前设备类型
 * @returns {'mobile'|'tablet'|'desktop'}
 */
export const getDeviceType = () => {
  if (isMobile()) return 'mobile'
  if (isTablet()) return 'tablet'
  return 'desktop'
}

/**
 * 检测是否为触摸设备
 * @returns {boolean}
 */
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 获取屏幕方向
 * @returns {'portrait'|'landscape'}
 */
export const getOrientation = () => {
  return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape'
}

/**
 * 检测是否为竖屏
 * @returns {boolean}
 */
export const isPortrait = () => {
  return getOrientation() === 'portrait'
}

/**
 * 检测是否为横屏
 * @returns {boolean}
 */
export const isLandscape = () => {
  return getOrientation() === 'landscape'
}

/**
 * 获取适合当前设备的图表高度
 * @param {number} defaultHeight - 默认高度
 * @returns {number}
 */
export const getChartHeight = (defaultHeight = 300) => {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return Math.min(defaultHeight * 0.7, 200)
    case 'tablet':
      return Math.min(defaultHeight * 0.85, 250)
    default:
      return defaultHeight
  }
}

/**
 * 获取适合当前设备的网格列数
 * @param {number} desktopCols - 桌面端列数
 * @param {number} tabletCols - 平板端列数
 * @param {number} mobileCols - 移动端列数
 * @returns {number}
 */
export const getGridColumns = (desktopCols = 4, tabletCols = 2, mobileCols = 1) => {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return mobileCols
    case 'tablet':
      return tabletCols
    default:
      return desktopCols
  }
}

/**
 * 获取适合当前设备的字体大小
 * @param {number} baseSize - 基础字体大小
 * @returns {number}
 */
export const getFontSize = (baseSize = 14) => {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return Math.max(baseSize * 0.9, 12)
    case 'tablet':
      return baseSize * 0.95
    default:
      return baseSize
  }
}

/**
 * 获取适合当前设备的间距
 * @param {number} baseSpacing - 基础间距
 * @returns {number}
 */
export const getSpacing = (baseSpacing = 16) => {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return Math.max(baseSpacing * 0.75, 8)
    case 'tablet':
      return baseSpacing * 0.9
    default:
      return baseSpacing
  }
}

/**
 * 监听屏幕尺寸变化
 * @param {Function} callback - 回调函数
 * @returns {Function} - 取消监听的函数
 */
export const onResize = (callback) => {
  let timeoutId = null
  
  const handleResize = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    
    timeoutId = setTimeout(() => {
      callback({
        width: window.innerWidth,
        height: window.innerHeight,
        deviceType: getDeviceType(),
        orientation: getOrientation(),
        isMobile: isMobile(),
        isTablet: isTablet(),
        isDesktop: isDesktop()
      })
    }, 100)
  }
  
  window.addEventListener('resize', handleResize)
  
  // 返回取消监听的函数
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    window.removeEventListener('resize', handleResize)
  }
}

/**
 * 获取适合当前设备的Chart.js配置
 * @param {Object} baseOptions - 基础配置
 * @returns {Object}
 */
export const getResponsiveChartOptions = (baseOptions = {}) => {
  const deviceType = getDeviceType()
  const isMobileDevice = deviceType === 'mobile'
  
  return {
    ...baseOptions,
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      ...baseOptions.plugins,
      legend: {
        ...baseOptions.plugins?.legend,
        position: isMobileDevice ? 'bottom' : (baseOptions.plugins?.legend?.position || 'top'),
        labels: {
          ...baseOptions.plugins?.legend?.labels,
          boxWidth: isMobileDevice ? 8 : 12,
          font: {
            size: getFontSize(12)
          },
          padding: getSpacing(10)
        }
      },
      tooltip: {
        ...baseOptions.plugins?.tooltip,
        titleFont: {
          size: getFontSize(14)
        },
        bodyFont: {
          size: getFontSize(12)
        },
        padding: getSpacing(12)
      }
    },
    scales: {
      ...baseOptions.scales,
      x: {
        ...baseOptions.scales?.x,
        ticks: {
          ...baseOptions.scales?.x?.ticks,
          font: {
            size: getFontSize(11)
          },
          maxTicksLimit: isMobileDevice ? 4 : 8,
          maxRotation: isMobileDevice ? 45 : 0
        }
      },
      y: {
        ...baseOptions.scales?.y,
        ticks: {
          ...baseOptions.scales?.y?.ticks,
          font: {
            size: getFontSize(11)
          },
          maxTicksLimit: isMobileDevice ? 5 : 8
        }
      }
    }
  }
}

/**
 * 获取Element Plus组件的响应式尺寸
 * @returns {'large'|'default'|'small'}
 */
export const getElementSize = () => {
  const deviceType = getDeviceType()
  
  switch (deviceType) {
    case 'mobile':
      return 'small'
    case 'tablet':
      return 'default'
    default:
      return 'default'
  }
}
