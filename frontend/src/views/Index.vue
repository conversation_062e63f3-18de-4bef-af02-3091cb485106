<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import DataCard from '../components/common/DataCard.vue'
import LineChart from '../components/charts/LineChart.vue'
import PieChart from '../components/charts/PieChart.vue'
import { dashboardAPI } from '../utils/api'
import { formatCurrency, formatPercent } from '../utils/format'
import { exportToPDF } from '../utils/export'

// 加载状态
const loading = ref(true)
const exporting = ref(false)

// 仪表盘数据
const dashboardData = reactive({
  compositeSignal: {
    signal: '',
    strength: 0,
    updateTime: ''
  },
  signalDistribution: [],
  indicatorAgreement: [],
  priceChart: {
    labels: [],
    prices: [],
    indicators: {}
  },
  movingAverages: [],
  oscillators: [],
  tradingRecommendation: {
    signal: '中立',
    bullishFactors: [],
    bearishFactors: [],
    recommendations: [],
    riskAlerts: []
  }
})

// 货币对选择
const currencyPair = ref('USDX')
const currencyPairs = [
  { value: 'USDX', label: '美元指数 (USDX)' },
  { value: 'EURUSD', label: '欧元/美元 (EURUSD)' },
  { value: 'USDJPY', label: '美元/日元 (USDJPY)' },
  { value: 'GBPUSD', label: '英镑/美元 (GBPUSD)' },
  { value: 'AUDUSD', label: '澳元/美元 (AUDUSD)' }
]

// 时间周期选择
const timePeriod = ref('daily')
const timePeriods = [
  { value: 'daily', label: '日线' },
  { value: 'weekly', label: '周线' },
  { value: '4h', label: '4小时' },
  { value: '1h', label: '1小时' },
  { value: '15m', label: '15分钟' }
]

// 信号分布图表数据
const signalPieChartData = ref({
  labels: [],
  datasets: [
    {
      backgroundColor: [],
      data: []
    }
  ]
})

// 价格图表数据
const priceChartData = ref({
  labels: [],
  datasets: []
})

// 一致性图表数据
const agreementChartData = ref({
  labels: [],
  datasets: [
    {
      label: '一致性百分比',
      backgroundColor: 'rgba(22, 93, 255, 0.7)',
      data: []
    }
  ]
})

// 获取仪表盘数据
const fetchDashboardData = async () => {
  loading.value = true
  try {
    const data = await dashboardAPI.getDashboardSummary({
      currencyPair: currencyPair.value,
      timePeriod: timePeriod.value
    })

    // 更新仪表盘数据
    Object.assign(dashboardData, data)

    // 更新信号分布图表数据
    updateSignalPieChart()

    // 更新价格图表数据
    updatePriceChart()

    // 更新一致性图表数据
    updateAgreementChart()

    loading.value = false
  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    loading.value = false
  }
}

// 更新信号分布图表
const updateSignalPieChart = () => {
  const labels = dashboardData.signalDistribution.map(item => item.type)
  const data = dashboardData.signalDistribution.map(item => item.count)
  const colors = dashboardData.signalDistribution.map(item => item.color)

  signalPieChartData.value = {
    labels,
    datasets: [
      {
        backgroundColor: colors,
        data
      }
    ]
  }
}

// 更新价格图表
const updatePriceChart = () => {
  const { labels, prices, indicators } = dashboardData.priceChart

  const datasets = [
    {
      label: '价格',
      borderColor: '#165DFF',
      backgroundColor: 'rgba(22, 93, 255, 0.1)',
      data: prices,
      fill: true
    }
  ]

  // 添加指标数据
  Object.entries(indicators).forEach(([key, values]) => {
    const colors = {
      SMA20: '#FF9A2E',
      EMA10: '#00C48F',
      'Hull MA9': '#8B5CF6',
      'BB Upper': '#F59E0B',
      'BB Middle': '#10B981',
      'BB Lower': '#F59E0B'
    }

    datasets.push({
      label: key,
      borderColor: colors[key] || '#86909C',
      backgroundColor: 'transparent',
      data: values,
      borderWidth: 2,
      pointRadius: 0,
      borderDash: key.includes('BB') && !key.includes('Middle') ? [5, 5] : []
    })
  })

  priceChartData.value = {
    labels,
    datasets
  }
}

// 更新一致性图表
const updateAgreementChart = () => {
  const labels = dashboardData.indicatorAgreement.map(item => item.category)
  const data = dashboardData.indicatorAgreement.map(item => item.agreement)

  agreementChartData.value = {
    labels,
    datasets: [
      {
        label: '一致性百分比',
        backgroundColor: 'rgba(22, 93, 255, 0.7)',
        data
      }
    ]
  }
}

// 刷新数据
const refreshData = () => {
  fetchDashboardData()
}

// 信号强度百分比样式
const signalStrengthStyle = computed(() => {
  return {
    width: `${dashboardData.compositeSignal.strength}%`
  }
})

// 信号类别样式
const getSignalClass = (type) => {
  const signalMap = {
    '强烈买入': 'strong-buy',
    '买入': 'buy',
    '中立': 'neutral',
    '卖出': 'sell',
    '强烈卖出': 'strong-sell'
  }

  return signalMap[type] || 'neutral'
}

// 导出报告
const exportReport = async () => {
  try {
    exporting.value = true
    const element = document.querySelector('.index-page')

    // 确保在导出前滚动到页面顶部
    const originalScrollPosition = window.scrollY
    window.scrollTo(0, 0)

    // 等待DOM更新完成
    await new Promise(resolve => setTimeout(resolve, 200))

    // 使用改进的导出函数
    await exportToPDF(element, '外汇技术指标分析报告', {
      scrollY: 0,
      scrollX: 0,
      windowWidth: document.documentElement.offsetWidth,
      windowHeight: document.documentElement.offsetHeight
    })

    // 恢复原始滚动位置
    window.scrollTo(0, originalScrollPosition)

    exporting.value = false
  } catch (error) {
    console.error('导出报告失败:', error)
    exporting.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchDashboardData()
})
</script>

<template>
  <AppLayout>
    <div class="index-page">
      <!-- 页面标题 -->
      <header class="page-header">
        <h1 class="page-title">外汇技术指标分析仪表盘</h1>
        <p class="page-description">整合震荡指标与平均线指标，提供专业交易决策支持</p>
      </header>

      <!-- 货币对选择和时间周期 -->
      <div class="filter-bar">
        <div class="filter-group">
          <span class="filter-label">货币对:</span>
          <el-select v-model="currencyPair" @change="refreshData" style="width: 180px;">
            <el-option
              v-for="item in currencyPairs"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="filter-group">
          <span class="filter-label">时间周期:</span>
          <el-select v-model="timePeriod" @change="refreshData" style="width: 120px;">
            <el-option
              v-for="item in timePeriods"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="filter-buttons">
          <el-button type="primary" @click="refreshData">
            <i class="fas fa-sync-alt"></i>
            刷新数据
          </el-button>
          <el-button :loading="exporting" @click="exportReport">
            <i class="fas fa-download"></i>
            导出报告
          </el-button>
        </div>
      </div>

      <!-- 主要信号区域 -->
      <div class="signal-cards">
        <!-- 综合信号 -->
        <DataCard title="综合信号" :loading="loading">
          <div class="composite-signal">
            <div class="signal-gauge">
              <div class="signal-bg"></div>
              <div class="signal-progress" :style="signalStrengthStyle"></div>
            </div>

            <div class="signal-value">{{ dashboardData.compositeSignal.signal }}</div>
            <div class="signal-strength">信号强度: {{ dashboardData.compositeSignal.strength }}%</div>

            <div class="signal-badges">
              <div
                v-for="(item, index) in ['强烈卖出', '卖出', '中立', '买入', '强烈买入']"
                :key="index"
                class="signal-badge"
                :class="[getSignalClass(item), { active: item === dashboardData.compositeSignal.signal }]"
              >
                {{ item }}
              </div>
            </div>

            <div class="update-time">更新于: {{ dashboardData.compositeSignal.updateTime }}</div>
          </div>
        </DataCard>

        <!-- 信号分布 -->
        <DataCard title="信号分布" :loading="loading">
          <PieChart :chart-data="signalPieChartData" :height="250" />
        </DataCard>

        <!-- 指标一致性 -->
        <DataCard title="指标一致性" :loading="loading">
          <LineChart :chart-data="agreementChartData" :height="250" />
        </DataCard>
      </div>

      <!-- 价格图表 -->
      <DataCard title="价格图表" :loading="loading">
        <LineChart :chart-data="priceChartData" :height="400" />
      </DataCard>

      <!-- 指标详情区域 -->
      <div class="indicators-section">
        <!-- 平均线指标 -->
        <DataCard title="平均线指标" :loading="loading">
          <div class="indicators-list">
            <div
              v-for="(item, index) in dashboardData.movingAverages"
              :key="index"
              class="indicator-item"
            >
              <div class="indicator-header">
                <div class="indicator-name">{{ item.name }}</div>
                <div class="indicator-signal" :class="getSignalClass(item.signal)">{{ item.signal }}</div>
              </div>

              <div class="indicator-values">
                <div
                  v-for="(value, key) in item.values"
                  :key="key"
                  class="value-item"
                >
                  <span class="value-label">{{ key }}:</span>
                  <span class="value-number">{{ value }}</span>
                </div>
              </div>

              <div class="indicator-analysis">{{ item.analysis }}</div>
            </div>
          </div>
        </DataCard>

        <!-- 震荡指标 -->
        <DataCard title="震荡指标" :loading="loading">
          <div class="indicators-list">
            <div
              v-for="(item, index) in dashboardData.oscillators"
              :key="index"
              class="indicator-item"
            >
              <div class="indicator-header">
                <div class="indicator-name">{{ item.name }}</div>
                <div class="indicator-signal" :class="getSignalClass(item.signal)">{{ item.signal }}</div>
              </div>

              <div class="indicator-values">
                <div
                  v-for="(value, key) in item.values"
                  :key="key"
                  class="value-item"
                >
                  <span class="value-label">{{ key }}:</span>
                  <span class="value-number">{{ value }}</span>
                </div>
              </div>

              <div class="indicator-analysis">{{ item.analysis }}</div>
            </div>
          </div>
        </DataCard>
      </div>

      <!-- 综合交易建议 -->
      <DataCard title="综合交易建议" :loading="loading" class="trading-recommendation">
        <div class="recommendation-header">
          <div class="recommendation-signal">
            <div class="signal-name">交易建议:</div>
            <div class="signal-value" :class="getSignalClass(dashboardData.tradingRecommendation?.signal || '中立')">
              {{ dashboardData.tradingRecommendation?.signal || '中立' }}
            </div>
            <div class="signal-note">基于12个技术指标综合分析</div>
          </div>

          <div class="recommendation-actions">
            <el-button size="small">
              <i class="fas fa-star"></i>
              添加到关注
            </el-button>
            <el-button size="small">
              <i class="fas fa-history"></i>
              信号历史
            </el-button>
          </div>
        </div>

        <div class="recommendation-grid">
          <div class="recommendation-card">
            <h3 class="recommendation-title">
              <i class="fas fa-arrow-up text-success"></i>
              看涨因素
            </h3>
            <ul class="recommendation-list">
              <li v-for="(item, index) in dashboardData.tradingRecommendation?.bullishFactors || []" :key="index">
                <i class="fas fa-check-circle text-success mr-2"></i> {{ item }}
              </li>
              <li v-if="!dashboardData.tradingRecommendation?.bullishFactors?.length" class="empty-list">
                暂无看涨因素
              </li>
            </ul>
          </div>

          <div class="recommendation-card">
            <h3 class="recommendation-title">
              <i class="fas fa-arrow-down text-danger"></i>
              看跌因素
            </h3>
            <ul class="recommendation-list">
              <li v-for="(item, index) in dashboardData.tradingRecommendation?.bearishFactors || []" :key="index">
                <i class="fas fa-times-circle text-danger mr-2"></i> {{ item }}
              </li>
              <li v-if="!dashboardData.tradingRecommendation?.bearishFactors?.length" class="empty-list">
                暂无看跌因素
              </li>
            </ul>
          </div>

          <div class="recommendation-card">
            <h3 class="recommendation-title">
              <i class="fas fa-lightbulb text-warning"></i>
              操作建议
            </h3>
            <ul class="recommendation-list">
              <li v-for="(item, index) in dashboardData.tradingRecommendation?.recommendations || []" :key="index">
                <i class="fas fa-caret-right mr-2"></i> {{ item }}
              </li>
              <li v-if="!dashboardData.tradingRecommendation?.recommendations?.length" class="empty-list">
                暂无具体建议
              </li>
            </ul>
          </div>
        </div>

        <div class="risk-alert">
          <h3 class="recommendation-title">
            <i class="fas fa-exclamation-triangle"></i>
            风险提示
          </h3>
          <ul class="recommendation-list">
            <li v-for="(item, index) in dashboardData.tradingRecommendation?.riskAlerts || []" :key="index">
              <i class="fas fa-exclamation-circle mr-2"></i> {{ item }}
            </li>
            <li v-if="!dashboardData.tradingRecommendation?.riskAlerts?.length" class="empty-list">
              暂无风险提示
            </li>
          </ul>
        </div>
      </DataCard>
    </div>
  </AppLayout>
</template>

<style scoped>
.index-page {
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  background: linear-gradient(to right, #165DFF, #0FC6C2);
  -webkit-background-clip: text;
  color: transparent;
}

.page-description {
  color: #666;
  font-size: 16px;
}

.filter-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
  gap: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.filter-label {
  margin-right: 8px;
  font-weight: 500;
  min-width: 70px;
}

.filter-buttons {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.signal-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.composite-signal {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 0;
}

.signal-gauge {
  width: 100%;
  height: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 16px;
  position: relative;
  overflow: hidden;
}

.signal-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, #D4380D, #F53F3F, #86909C, #00A870, #00B42A);
  opacity: 0.2;
}

.signal-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(to right, #D4380D, #F53F3F, #86909C, #00A870, #00B42A);
  transition: width 0.5s;
}

.signal-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.signal-strength {
  color: #666;
  margin-bottom: 16px;
}

.signal-badges {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;
}

.signal-badge {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  opacity: 0.6;
  transition: all 0.3s;
}

.signal-badge.active {
  opacity: 1;
  transform: scale(1.1);
  font-weight: bold;
}

.signal-badge.strong-buy {
  background-color: rgba(0, 180, 42, 0.1);
  color: #00B42A;
}

.signal-badge.buy {
  background-color: rgba(0, 168, 112, 0.1);
  color: #00A870;
}

.signal-badge.neutral {
  background-color: rgba(134, 144, 156, 0.1);
  color: #86909C;
}

.signal-badge.sell {
  background-color: rgba(245, 63, 63, 0.1);
  color: #F53F3F;
}

.signal-badge.strong-sell {
  background-color: rgba(212, 56, 13, 0.1);
  color: #D4380D;
}

.update-time {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}

.indicators-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 24px;
}

.indicators-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.indicator-item {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 16px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.indicator-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.indicator-name {
  font-weight: bold;
}

.indicator-signal {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
}

.indicator-values {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.value-item {
  background-color: rgba(0, 0, 0, 0.03);
  padding: 8px;
  border-radius: 4px;
  font-size: 13px;
}

.value-label {
  color: #666;
  margin-right: 4px;
}

.value-number {
  font-weight: 500;
}

.indicator-analysis {
  color: #666;
  font-size: 13px;
  line-height: 1.5;
}

.trading-recommendation {
  margin-top: 24px;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.recommendation-signal {
  display: flex;
  align-items: center;
  gap: 12px;
}

.signal-name {
  font-weight: bold;
  font-size: 16px;
}

.signal-value {
  font-size: 20px;
  font-weight: bold;
  padding: 4px 12px;
  border-radius: 16px;
}

.signal-value.strong-buy {
  background-color: rgba(0, 180, 42, 0.1);
  color: #00B42A;
}

.signal-value.buy {
  background-color: rgba(0, 168, 112, 0.1);
  color: #00A870;
}

.signal-value.neutral {
  background-color: rgba(134, 144, 156, 0.1);
  color: #86909C;
}

.signal-value.sell {
  background-color: rgba(245, 63, 63, 0.1);
  color: #F53F3F;
}

.signal-value.strong-sell {
  background-color: rgba(212, 56, 13, 0.1);
  color: #D4380D;
}

.signal-note {
  color: #666;
  font-size: 14px;
  margin-left: 4px;
}

.recommendation-actions {
  display: flex;
  gap: 8px;
}

.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.recommendation-card {
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 16px;
  height: 100%;
}

.recommendation-title {
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.recommendation-title i {
  margin-right: 8px;
  font-size: 14px;
}

.recommendation-list {
  list-style: none;
  padding-left: 0;
  color: #666;
  font-size: 14px;
}

.recommendation-list li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.risk-alert {
  margin-top: 16px;
  background-color: rgba(245, 63, 63, 0.05);
  border-radius: 8px;
  padding: 16px;
}

.risk-alert .recommendation-title {
  color: #F53F3F;
  display: flex;
  align-items: center;
}

.risk-alert .recommendation-title::before {
  content: "";
}

.text-success {
  color: #00B42A;
}

.text-warning {
  color: #FF9A2E;
}

.text-danger {
  color: #F53F3F;
}

.mr-2 {
  margin-right: 8px;
}

.empty-list {
  color: #999;
  font-style: italic;
}

@media (max-width: 1200px) {
  .signal-cards,
  .indicators-section,
  .recommendation-grid {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    width: 100%;
  }

  .filter-buttons {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }

  .filter-label {
    min-width: 80px;
  }

  :deep(.el-select) {
    width: 100% !important;
  }

  .recommendation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .recommendation-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 0;
  }

  .signal-cards {
    gap: var(--mobile-margin, 8px);
    margin-bottom: 16px;
  }

  .signal-card {
    padding: var(--mobile-padding, 12px);
  }

  .signal-value {
    font-size: 18px;
  }

  .signal-change {
    font-size: 12px;
  }

  .indicators-section {
    gap: var(--mobile-margin, 8px);
    margin-bottom: 16px;
  }

  .filter-bar {
    padding: var(--mobile-padding, 12px);
    gap: 8px;
  }

  .filter-group {
    flex-direction: column;
    gap: 4px;
  }

  .filter-label {
    font-size: 13px;
    min-width: auto;
  }

  .filter-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .filter-buttons .el-button {
    width: 100%;
    margin: 0;
  }

  .recommendation-grid {
    gap: var(--mobile-margin, 8px);
  }

  .recommendation-header {
    padding: var(--mobile-padding, 12px);
    gap: 8px;
  }

  .recommendation-title {
    font-size: 16px;
  }

  .recommendation-actions {
    flex-direction: column;
    gap: 8px;
  }

  .recommendation-actions .el-button {
    width: 100%;
    margin: 0;
  }

  .recommendation-item {
    padding: var(--mobile-padding, 12px);
  }

  .recommendation-header-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .recommendation-pair {
    font-size: 14px;
  }

  .recommendation-type {
    font-size: 12px;
  }

  .recommendation-details {
    gap: 8px;
  }

  .recommendation-detail {
    font-size: 12px;
  }

  .recommendation-detail-label {
    min-width: 60px;
  }

  .recommendation-confidence {
    font-size: 12px;
  }

  .recommendation-time {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .signal-card {
    padding: 10px;
  }

  .signal-icon {
    width: 32px;
    height: 32px;
  }

  .signal-icon i {
    font-size: 14px;
  }

  .signal-value {
    font-size: 16px;
  }

  .signal-label {
    font-size: 11px;
  }

  .signal-change {
    font-size: 11px;
  }

  .filter-bar {
    padding: 10px;
  }

  .recommendation-item {
    padding: 10px;
  }

  .recommendation-pair {
    font-size: 13px;
  }

  .recommendation-details {
    flex-direction: column;
    gap: 4px;
  }

  .recommendation-detail {
    font-size: 11px;
  }
}
</style>
