import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { codeInspectorPlugin } from 'code-inspector-plugin';
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [codeInspectorPlugin({
    bundler: 'vite',
  }),
    vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    host: '0.0.0.0', // 允许外部访问
    proxy: {
      '/api': {
        target: 'http://localhost:8088',
        changeOrigin: true,
      }
    }
  }
})
