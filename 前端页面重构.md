参照D:\fe\fe\html\index.html 完善Index.vue页面  补充缺失的‘船体移动平均线 (Hull MA)’，‘
布林带 (Bollinger Bands)’和‘综合交易建议’模块，并完善java后端逻辑，后端暂时使用mock数据。


参照D:\fe\fe\html\fx_finance_dashboard.html 完善FinanceDashboard.vue页面  补充缺失的‘已实现损益详情’，‘
现金流量汇率影响’和‘数据输入面板’模块，并完善java后端逻辑，后端暂时使用mock数据。


参照D:\fe\fe\html\fx_finance_dashboard.html 完善FinanceDashboard.vue页面的‘外币资产详情’，‘远期合约’模块，并完善java后端逻辑，后端暂时使用mock数据。
修复‘已实现损益详情’，‘现金流量汇率影响’和‘数据输入面板’模块的样式，保持和D:\fe\fe\html\fx_finance_dashboard.html样式一致


参照D:\fe\fe\html\/fx_risk_dashboard.html重构RiskDashboard.vue页面，重构后包含模块：净美元敞口，汇率波动率,95% VaR,风险等级,汇率变化分析,风险量化模型,风险仪表盘,结汇建议。
完善重构后对应模块的java后端逻辑，后端暂时使用mock数据。

风险量化模型模块中，VaR模型，马尔可夫链，马科维茨有效前沿技术指标不全，请在页面不全，并补充java代码中的mock数据

以下图表没有展示出来，请完善前端展示：风险量化模型中的VaR分布图，状态转移图，有效前沿曲线；风险仪表盘中的雷达图显示综合风险评估。
并完善java后端逻辑，后端暂时使用mock数据。


重构FinanceDashboard.vue页面的‘外币资产详情’和‘远期合约’模块的功能和样式，参照D:\fe\fe\html\fx_finance_dashboard.html 
